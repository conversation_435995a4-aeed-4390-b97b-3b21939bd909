"use client";

import { useMutation } from "@tanstack/react-query";
import { Input } from "@/components/ui/input";
// This import path assumes you have a component for input in your project structure
import { But<PERSON> } from "@/components/ui/button";
import { useTRPC } from "@/trpc/client";
import { toast } from "sonner";
import { useState } from "react";

const Page = () => {
  const [value, setValue] = useState("");

  const trpc = useTRPC();
  const invoke = useMutation(trpc.invoke.mutationOptions({
    onSuccess: () => {
      toast.success("Background job started!");
    }
  }));

  return (
    <div className="p-4 max-w-7xl mx-auto">
      <Input value={value} onChange={(e) => setValue(e.target.value)} />
      <Button disabled={invoke.isPending}onClick={() => invoke.mutate({ value: value })}>
        Invoke Background Job
      </Button>
    </div>
    );
};

export default Page;  