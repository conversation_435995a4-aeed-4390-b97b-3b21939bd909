# This is a config for E2B sandbox template.
# You can use template ID (kv8868fpzqjs0m3cp4fm) or template name (fm-olt-nextjs-003-ghcd) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("fm-olt-nextjs-003-ghcd") # Sync sandbox
# sandbox = await AsyncSandbox.create("fm-olt-nextjs-003-ghcd") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('fm-olt-nextjs-003-ghcd')

team_id = "9de397f1-0541-4e0f-8aa1-741e42ae078b"
start_cmd = "/compile_page.sh"
dockerfile = "e2b.Dockerfile"
template_name = "fm-olt-nextjs-003-ghcd"
template_id = "kv8868fpzqjs0m3cp4fm"
